@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.inp-country {
	position: relative;
	&__select {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		min-height: 50px;
		opacity: 0;
	}
	&__box {
		display: flex;
		gap: 10px;
		align-items: center;
		height: 100%;
		min-height: 50px;
		padding: 5px 20px 5px 10px;
		border: 2px solid variables.$color-bd;
		background-color: variables.$color-white;
		background-image: url(variables.$svg-select);
		background-position: top 50% right 15px;
		background-repeat: no-repeat;
		background-size: 16px 9px;
	}
	&__flag {
		width: 25px;
		height: 17px;
		box-shadow: 0 0 3px rgba(variables.$color-black, 0.2);
	}
}
